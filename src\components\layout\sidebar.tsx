'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useSession } from 'next-auth/react'
import {
  Package,
  Palette,
  FileImage,
  Settings,
  ChevronLeft,
  ChevronRight,
  Home,
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { useUIStore } from '@/stores/ui-store'
import { cn } from '@/lib/utils'

const navigation = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: Home,
    description: 'Overview and statistics',
  },
  {
    name: 'Orders',
    href: '/dashboard/orders',
    icon: Package,
    description: 'Manage orders and processing',
  },
  {
    name: 'Designs',
    href: '/dashboard/designs',
    icon: Palette,
    description: 'Design library and management',
  },
  {
    name: 'Templates',
    href: '/dashboard/templates',
    icon: FileImage,
    description: 'Product templates and layouts',
  },
  {
    name: 'Settings',
    href: '/dashboard/settings',
    icon: Settings,
    description: 'Application settings',
  },
]

export function Sidebar() {
  const pathname = usePathname()
  const { data: session } = useSession()
  const { sidebarCollapsed, setSidebarCollapsed } = useUIStore()

  // Generate user initials
  const getUserInitials = (name?: string | null, email?: string | null) => {
    if (name) {
      return name
        .split(' ')
        .map(n => n[0])
        .join('')
        .toUpperCase()
        .slice(0, 2)
    }
    if (email) {
      return email[0].toUpperCase()
    }
    return 'U'
  }

  const userInitials = getUserInitials(
    session?.user?.name,
    session?.user?.email
  )

  return (
    <div className="flex h-full flex-col border-r border-gray-200 bg-white shadow-sm">
      {/* Logo and Toggle */}
      <div className="flex items-center justify-between border-b border-gray-200 p-4">
        {!sidebarCollapsed && (
          <div className="flex items-center space-x-3">
            <div className="flex h-9 w-9 items-center justify-center rounded-lg bg-gradient-to-br from-blue-600 to-blue-700 shadow-sm">
              <span className="text-sm font-bold text-white">RP</span>
            </div>
            <div className="flex flex-col">
              <span className="text-lg font-semibold text-gray-900">
                RavPrints
              </span>
              <span className="text-xs text-gray-500">Print Automation</span>
            </div>
          </div>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
          className="p-1.5 hover:bg-gray-100"
        >
          {sidebarCollapsed ? (
            <ChevronRight className="h-4 w-4" />
          ) : (
            <ChevronLeft className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 p-4">
        {navigation.map(item => {
          const isActive = pathname === item.href
          const Icon = item.icon

          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'flex items-center space-x-3 rounded-lg px-3 py-3 text-sm font-medium transition-all duration-200',
                isActive
                  ? 'border border-blue-200 bg-blue-50 text-blue-700 shadow-sm'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm'
              )}
              title={sidebarCollapsed ? item.name : undefined}
            >
              <Icon
                className={cn(
                  'h-5 w-5 flex-shrink-0 transition-colors',
                  isActive ? 'text-blue-600' : 'text-gray-500'
                )}
              />
              {!sidebarCollapsed && (
                <div className="min-w-0 flex-1">
                  <div className="truncate font-medium">{item.name}</div>
                  <div className="mt-0.5 truncate text-xs text-gray-500">
                    {item.description}
                  </div>
                </div>
              )}
            </Link>
          )
        })}
      </nav>

      {/* User Info */}
      {session?.user && (
        <div className="border-t border-gray-200 bg-gray-50 p-4">
          {!sidebarCollapsed ? (
            <div className="flex items-center space-x-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src="" alt={session.user.name || 'User'} />
                <AvatarFallback className="bg-blue-600 text-sm font-medium text-white">
                  {userInitials}
                </AvatarFallback>
              </Avatar>
              <div className="min-w-0 flex-1">
                <div className="truncate text-sm font-medium text-gray-900">
                  {session.user.name || 'User'}
                </div>
                <div className="truncate text-xs text-gray-500">
                  {session.user.email}
                </div>
                <Badge variant="secondary" className="mt-1 text-xs">
                  {session.user.role}
                </Badge>
              </div>
            </div>
          ) : (
            <div className="flex justify-center">
              <Avatar className="h-8 w-8">
                <AvatarImage src="" alt={session.user.name || 'User'} />
                <AvatarFallback className="bg-blue-600 text-xs font-medium text-white">
                  {userInitials}
                </AvatarFallback>
              </Avatar>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
