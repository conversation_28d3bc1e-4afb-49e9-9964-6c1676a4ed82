import { create } from 'zustand'
import { Order, OrderStatus, Platform, ProductType } from '@prisma/client'

interface OrderFilters {
  status: OrderStatus | 'all'
  platform: Platform | 'all'
  productType: ProductType | 'all'
  search?: string
}

interface OrderStore {
  orders: Order[]
  selectedOrders: string[]
  filters: OrderFilters
  loading: boolean

  // Actions
  setOrders: (orders: Order[]) => void
  addOrders: (orders: Order[]) => void
  updateOrder: (id: string, updates: Partial<Order>) => void
  selectOrder: (id: string) => void
  selectMultiple: (ids: string[]) => void
  clearSelection: () => void
  setFilters: (filters: Partial<OrderFilters>) => void
  setLoading: (loading: boolean) => void

  // Computed
  filteredOrders: () => Order[]
}

export const useOrderStore = create<OrderStore>((set, get) => ({
  orders: [],
  selectedOrders: [],
  filters: { status: 'all', platform: 'all', productType: 'all' },
  loading: false,

  setOrders: orders => set({ orders }),

  addOrders: newOrders =>
    set(state => ({
      orders: [...state.orders, ...newOrders],
    })),

  updateOrder: (id, updates) =>
    set(state => ({
      orders: state.orders.map(order =>
        order.id === id ? { ...order, ...updates } : order
      ),
    })),

  selectOrder: id =>
    set(state => ({
      selectedOrders: state.selectedOrders.includes(id)
        ? state.selectedOrders.filter(orderId => orderId !== id)
        : [...state.selectedOrders, id],
    })),

  selectMultiple: ids => set({ selectedOrders: ids }),
  clearSelection: () => set({ selectedOrders: [] }),
  setFilters: filters =>
    set(state => ({ filters: { ...state.filters, ...filters } })),
  setLoading: loading => set({ loading }),

  filteredOrders: () => {
    const { orders, filters } = get()
    return orders.filter(order => {
      if (filters.status !== 'all' && order.status !== filters.status)
        return false
      if (filters.platform !== 'all' && order.platform !== filters.platform)
        return false
      if (
        filters.productType !== 'all' &&
        order.productType !== filters.productType
      )
        return false
      if (
        filters.search &&
        !order.orderId.toLowerCase().includes(filters.search.toLowerCase()) &&
        !order.customerName
          ?.toLowerCase()
          .includes(filters.search.toLowerCase())
      )
        return false
      return true
    })
  },
}))
