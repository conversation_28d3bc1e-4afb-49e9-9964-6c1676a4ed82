import { Template, ProductType } from '@prisma/client'

export interface TemplateSpecs {
  printAreaX: number
  printAreaY: number
  printWidth: number
  printHeight: number
  outputWidth: number
  outputHeight: number
  outputDPI: number
}

export interface TemplateCreateData {
  productType: ProductType
  name: string
  filePath: string
  printAreaX: number
  printAreaY: number
  printWidth: number
  printHeight: number
  outputWidth: number
  outputHeight: number
  outputDPI?: number
  isDefault?: boolean
  isActive?: boolean
}

export interface TemplateFilters {
  productType?: ProductType
  isActive?: boolean
  isDefault?: boolean
}

export type {
  Template,
  ProductType
}
