import { PrismaClient, UserRole, Platform, ProductType, OrderStatus } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seed...')

  // Clear existing data
  await prisma.order.deleteMany()
  await prisma.design.deleteMany()
  await prisma.template.deleteMany()
  await prisma.user.deleteMany()

  console.log('🗑️  Cleared existing data')

  // Create test users
  const hashedPassword = await bcrypt.hash('password123', 12)
  
  const staffUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: '<PERSON>',
      password: hashedPassword,
      role: UserRole.STAFF,
    },
  })

  const adminUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: '<PERSON>',
      password: hashedPassword,
      role: UserRole.ADMIN,
    },
  })

  console.log('👥 Created test users')

  // Create designs
  const designs = await Promise.all([
    prisma.design.create({
      data: {
        code: 'DSN-001',
        filename: 'cool-cat-design.png',
        originalName: 'Cool Cat Design.png',
        filePath: '/storage/designs/cool-cat-design.png',
        fileSize: 2048000,
        mimeType: 'image/png',
        tags: ['cat', 'funny', 'animal'],
        description: 'Cute cat design with sunglasses',
        category: 'Animals',
        width: 3600,
        height: 4800,
        dpi: 300,
      },
    }),
    prisma.design.create({
      data: {
        code: 'DSN-002',
        filename: 'motivational-quote.png',
        originalName: 'Motivational Quote.png',
        filePath: '/storage/designs/motivational-quote.png',
        fileSize: 1536000,
        mimeType: 'image/png',
        tags: ['quote', 'motivation', 'text'],
        description: 'Inspirational quote design',
        category: 'Quotes',
        width: 3600,
        height: 4800,
        dpi: 300,
      },
    }),
    prisma.design.create({
      data: {
        code: 'DSN-003',
        filename: 'geometric-pattern.svg',
        originalName: 'Geometric Pattern.svg',
        filePath: '/storage/designs/geometric-pattern.svg',
        fileSize: 512000,
        mimeType: 'image/svg+xml',
        tags: ['geometric', 'pattern', 'abstract'],
        description: 'Modern geometric pattern',
        category: 'Patterns',
        width: 3600,
        height: 4800,
        dpi: 300,
      },
    }),
    prisma.design.create({
      data: {
        code: 'DSN-004',
        filename: 'coffee-lover.png',
        originalName: 'Coffee Lover.png',
        filePath: '/storage/designs/coffee-lover.png',
        fileSize: 1800000,
        mimeType: 'image/png',
        tags: ['coffee', 'drink', 'lifestyle'],
        description: 'Perfect for coffee enthusiasts',
        category: 'Lifestyle',
        width: 1200,
        height: 1200,
        dpi: 300,
      },
    }),
  ])

  console.log('🎨 Created design library')

  // Create templates
  const templates = await Promise.all([
    prisma.template.create({
      data: {
        productType: ProductType.TSHIRT,
        name: 'T-Shirt Front Print',
        filePath: '/public/templates/tshirt/front-template.png',
        printAreaX: 600,
        printAreaY: 400,
        printWidth: 3600,
        printHeight: 4800,
        outputWidth: 4800,
        outputHeight: 6000,
        outputDPI: 300,
        isDefault: true,
        isActive: true,
      },
    }),
    prisma.template.create({
      data: {
        productType: ProductType.MUG,
        name: 'Mug Standard Template',
        filePath: '/public/templates/mug/standard-template.png',
        printAreaX: 200,
        printAreaY: 300,
        printWidth: 1200,
        printHeight: 1200,
        outputWidth: 1600,
        outputHeight: 1800,
        outputDPI: 300,
        isDefault: true,
        isActive: true,
      },
    }),
    prisma.template.create({
      data: {
        productType: ProductType.MOUSEPAD,
        name: 'Mousepad Rectangle',
        filePath: '/public/templates/mousepad/rectangle-template.png',
        printAreaX: 100,
        printAreaY: 100,
        printWidth: 2400,
        printHeight: 1800,
        outputWidth: 2600,
        outputHeight: 2000,
        outputDPI: 300,
        isDefault: true,
        isActive: true,
      },
    }),
    prisma.template.create({
      data: {
        productType: ProductType.TOTE_BAG,
        name: 'Tote Bag Standard',
        filePath: '/public/templates/tote-bag/standard-template.png',
        printAreaX: 400,
        printAreaY: 500,
        printWidth: 3000,
        printHeight: 3000,
        outputWidth: 3800,
        outputHeight: 4000,
        outputDPI: 300,
        isDefault: true,
        isActive: true,
      },
    }),
  ])

  console.log('📋 Created product templates')

  // Create sample orders
  const orders = await Promise.all([
    prisma.order.create({
      data: {
        orderId: 'LAZ-2024-001',
        platform: Platform.LAZADA,
        productType: ProductType.TSHIRT,
        size: 'L',
        designCode: 'DSN-001',
        quantity: 2,
        customerName: 'Alice Johnson',
        customerInfo: {
          phone: '+60123456789',
          address: '123 Main St, Kuala Lumpur',
          notes: 'Please use bright colors'
        },
        status: OrderStatus.MATCHED,
        matched: true,
        processed: false,
        designId: designs[0].id,
        userId: staffUser.id,
      },
    }),
    prisma.order.create({
      data: {
        orderId: 'SHP-2024-002',
        platform: Platform.SHOPEE,
        productType: ProductType.MUG,
        designCode: 'DSN-004',
        quantity: 1,
        customerName: 'Bob Chen',
        customerInfo: {
          phone: '+60198765432',
          address: '456 Coffee Ave, Petaling Jaya',
          notes: 'Gift wrapping requested'
        },
        status: OrderStatus.READY,
        matched: true,
        processed: true,
        designId: designs[3].id,
        layoutFileUrl: '/storage/exports/SHP-2024-002-layout.png',
        userId: staffUser.id,
      },
    }),
    prisma.order.create({
      data: {
        orderId: 'TIK-2024-003',
        platform: Platform.TIKTOK,
        productType: ProductType.MOUSEPAD,
        size: 'Standard',
        designCode: 'DSN-003',
        quantity: 3,
        customerName: 'Carol Wong',
        customerInfo: {
          phone: '+60187654321',
          address: '789 Tech Park, Cyberjaya'
        },
        status: OrderStatus.PROCESSING,
        matched: true,
        processed: false,
        designId: designs[2].id,
        userId: adminUser.id,
      },
    }),
    prisma.order.create({
      data: {
        orderId: 'MAN-2024-004',
        platform: Platform.MANUAL,
        productType: ProductType.TOTE_BAG,
        designCode: 'DSN-002',
        quantity: 5,
        customerName: 'David Lim',
        customerInfo: {
          phone: '+60176543210',
          address: '321 Shopping Mall, Subang Jaya',
          notes: 'Bulk order for event'
        },
        status: OrderStatus.PENDING,
        matched: false,
        processed: false,
        userId: adminUser.id,
      },
    }),
    prisma.order.create({
      data: {
        orderId: 'LAZ-2024-005',
        platform: Platform.LAZADA,
        productType: ProductType.TSHIRT,
        size: 'M',
        designCode: 'DSN-001',
        quantity: 1,
        customerName: 'Emma Taylor',
        customerInfo: {
          phone: '+60165432109',
          address: '654 University Drive, Shah Alam'
        },
        status: OrderStatus.EXPORTED,
        matched: true,
        processed: true,
        designId: designs[0].id,
        layoutFileUrl: '/storage/exports/LAZ-2024-005-layout.png',
        userId: staffUser.id,
      },
    }),
  ])

  console.log('📦 Created sample orders')

  console.log('✅ Database seeded successfully!')
  console.log('\n📊 Summary:')
  console.log(`   👥 Users: 2 (1 STAFF, 1 ADMIN)`)
  console.log(`   🎨 Designs: ${designs.length}`)
  console.log(`   📋 Templates: ${templates.length}`)
  console.log(`   📦 Orders: ${orders.length}`)
  console.log('\n🔐 Test Credentials:')
  console.log('   Staff: <EMAIL> / password123')
  console.log('   Admin: <EMAIL> / password123')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
