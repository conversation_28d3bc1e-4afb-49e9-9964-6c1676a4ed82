import { Order, OrderStatus, Platform, ProductType, Design, User } from '@prisma/client'

export type OrderWithRelations = Order & {
  design?: Design | null
  user: User
}

export interface OrderUploadData {
  orderId: string
  platform: Platform
  productType: ProductType
  size?: string
  designCode?: string
  quantity: number
  customerName?: string
  customerInfo?: any
}

export interface OrderFilters {
  status?: OrderStatus | 'all'
  platform?: Platform | 'all'
  productType?: ProductType | 'all'
  search?: string
  dateFrom?: Date
  dateTo?: Date
}

export interface OrderStats {
  total: number
  pending: number
  matched: number
  processing: number
  ready: number
  exported: number
  error: number
}

export interface BatchOperation {
  type: 'match' | 'export' | 'delete' | 'update_status'
  orderIds: string[]
  data?: any
}

export type {
  Order,
  OrderStatus,
  Platform,
  ProductType
}
