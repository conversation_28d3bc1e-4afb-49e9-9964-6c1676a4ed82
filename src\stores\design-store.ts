import { create } from 'zustand'
import { Design } from '@prisma/client'

interface DesignStore {
  designs: Design[]
  searchQuery: string
  selectedDesign: Design | null
  loading: boolean
  categories: string[]

  // Actions
  setDesigns: (designs: Design[]) => void
  addDesign: (design: Design) => void
  updateDesign: (id: string, updates: Partial<Design>) => void
  removeDesign: (id: string) => void
  setSearchQuery: (query: string) => void
  setSelectedDesign: (design: Design | null) => void
  setLoading: (loading: boolean) => void
  setCategories: (categories: string[]) => void

  // Computed
  searchDesigns: (query?: string) => Design[]
  getDesignsByCategory: (category: string) => Design[]
}

export const useDesignStore = create<DesignStore>((set, get) => ({
  designs: [],
  searchQuery: '',
  selectedDesign: null,
  loading: false,
  categories: [],

  setDesigns: designs => set({ designs }),

  addDesign: design =>
    set(state => ({
      designs: [...state.designs, design],
    })),

  updateDesign: (id, updates) =>
    set(state => ({
      designs: state.designs.map(design =>
        design.id === id ? { ...design, ...updates } : design
      ),
    })),

  removeDesign: id =>
    set(state => ({
      designs: state.designs.filter(design => design.id !== id),
    })),

  setSearchQuery: searchQuery => set({ searchQuery }),
  setSelectedDesign: selectedDesign => set({ selectedDesign }),
  setLoading: loading => set({ loading }),
  setCategories: categories => set({ categories }),

  searchDesigns: query => {
    const { designs, searchQuery } = get()
    const searchTerm = query || searchQuery

    if (!searchTerm) return designs

    return designs.filter(
      design =>
        design.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
        design.filename.toLowerCase().includes(searchTerm.toLowerCase()) ||
        design.originalName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        design.tags.some(tag =>
          tag.toLowerCase().includes(searchTerm.toLowerCase())
        ) ||
        design.description?.toLowerCase().includes(searchTerm.toLowerCase())
    )
  },

  getDesignsByCategory: category => {
    const { designs } = get()
    return designs.filter(design => design.category === category)
  },
}))
