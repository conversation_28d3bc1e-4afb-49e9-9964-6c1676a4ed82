import { Design } from '@prisma/client'

export interface DesignUploadData {
  code: string
  filename: string
  originalName?: string
  filePath: string
  fileSize?: number
  mimeType?: string
  tags?: string[]
  description?: string
  category?: string
  width?: number
  height?: number
  dpi?: number
}

export interface DesignMatchResult {
  design: Design
  confidence: number
  matchType: 'exact' | 'pattern' | 'tag' | 'fuzzy'
}

export interface DesignFilters {
  category?: string
  tags?: string[]
  search?: string
  mimeType?: string
  minDpi?: number
}

export interface DesignStats {
  total: number
  categories: Record<string, number>
  formats: Record<string, number>
  averageDpi: number
  totalSize: number
}

export type {
  Design
}
