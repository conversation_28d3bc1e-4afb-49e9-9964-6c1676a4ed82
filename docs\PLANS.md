# RavPrints - Made-to-Order Printing Business Automation MVP

## Project Overview

**Business Context:** Local-first web application to automate manual order processing workflow for customized products (t-shirts, mousepads, tote bags, mugs) from multiple e-commerce platforms (Lazada/Shopee/TikTok).

**Success Metrics:**
- Reduce order processing time from 10+ minutes to under 2 minutes per order
- Eliminate manual Photoshop steps for standard products
- Support batch processing of 50+ orders efficiently
- Maintain 300 DPI minimum print quality standards

---

## 1. MVP Feature Set & User Stories

### 1.1 Core Features

| Feature Group | User Story | Acceptance Criteria | Priority |
|---------------|------------|-------------------|----------|
| **Order Management** | As a staff member, I want to upload CSV/XLSX order files from multiple platforms so I can process orders in batches | • Support CSV/XLSX from Lazada/Shopee/TikTok<br>• Parse order details: ID, product type, size, design name, customer info<br>• Display orders in dashboard with status indicators<br>• Handle 50+ orders per batch | P0 |
| **Design Matching** | As a staff member, I want the system to automatically match design files to orders so I don't have to manually search for each design | • Auto-match using filename/tag/code patterns<br>• Support various naming conventions<br>• Flag unmatched orders for manual review<br>• Show match confidence scores | P0 |
| **Template Processing** | As a staff member, I want designs automatically resized to correct product dimensions so I don't need Photoshop | • Predefined templates for each product type<br>• Automatic dimension calculation<br>• Maintain aspect ratios and print quality<br>• Support different print areas (center, wrap, etc.) | P0 |
| **Preview System** | As a staff member, I want to preview the final layout before processing so I can catch errors early | • Real-time preview of design on product template<br>• Zoom and pan functionality<br>• Side-by-side comparison view<br>• Print specifications display (DPI, dimensions) | P0 |
| **Export Functionality** | As a staff member, I want to generate print-ready files for download so I can send them directly to the printer | • Export as high-resolution PNG/PDF<br>• Batch export with ZIP download<br>• Include order metadata in filenames<br>• 300 DPI minimum quality | P0 |
| **Manual Override** | As a staff member, I want to manually select designs when auto-matching fails so I can handle edge cases | • Search designs by name/tag/code<br>• Visual design picker with thumbnails<br>• Replace matched design with manual selection<br>• Update order status after manual intervention | P1 |

### 1.2 Advanced Features (Future Phases)

| Feature Group | User Story | Priority |
|---------------|------------|----------|
| **Batch Operations** | Process multiple orders simultaneously | P2 |
| **Custom Templates** | Create and modify product templates | P2 |
| **Quality Control** | Flag potential print quality issues | P2 |
| **Reporting** | Generate processing reports and analytics | P3 |

---

## 2. Technical Architecture

### 2.1 Technology Stack

**Frontend:**
- Next.js 15 with App Router
- TypeScript for type safety
- Tailwind CSS v4 for styling
- shadcn/ui for component library
- Zustand for state management

**Backend:**
- Next.js API routes
- Prisma ORM for database operations
- Supabase (PostgreSQL) for data storage
- NextAuth.js for authentication

**Image Processing:**
- Sharp.js for server-side image manipulation
- Canvas API for client-side previews
- PDF-lib for PDF generation

**File Handling:**
- SheetJS (xlsx) for Excel parsing
- Multer for file uploads
- Archiver for ZIP creation

### 2.2 Database Schema (Prisma)

```prisma
// User Management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  role      UserRole @default(STAFF)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  orders    Order[]
  
  @@map("users")
}

enum UserRole {
  STAFF
  ADMIN
}

// Order Management
model Order {
  id           String      @id @default(cuid())
  orderId      String      @unique // Platform order ID
  platform     Platform    // Source platform
  productType  ProductType
  size         String?
  designCode   String?
  quantity     Int         @default(1)
  customerName String?
  customerInfo Json?       // Flexible customer data
  
  // Processing Status
  status       OrderStatus @default(PENDING)
  matched      Boolean     @default(false)
  processed    Boolean     @default(false)
  
  // File References
  designId     String?
  design       Design?     @relation(fields: [designId], references: [id])
  layoutFileUrl String?    // Generated layout file path
  
  // Metadata
  userId       String
  user         User        @relation(fields: [userId], references: [id])
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  
  @@map("orders")
}

enum Platform {
  LAZADA
  SHOPEE
  TIKTOK
  MANUAL
}

enum ProductType {
  TSHIRT
  MUG
  MOUSEPAD
  TOTE_BAG
  CUSTOM
}

enum OrderStatus {
  PENDING
  MATCHED
  PROCESSING
  READY
  EXPORTED
  ERROR
}

// Design Management
model Design {
  id          String   @id @default(cuid())
  code        String   @unique // Design identifier
  filename    String
  originalName String?
  filePath    String   // Local file system path
  fileSize    Int?
  mimeType    String?
  
  // Metadata
  tags        String[] // Searchable tags
  description String?
  category    String?
  
  // Dimensions
  width       Int?
  height      Int?
  dpi         Int?
  
  // Relationships
  orders      Order[]
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("designs")
}

// Product Templates
model Template {
  id          String      @id @default(cuid())
  productType ProductType
  name        String
  filePath    String      // Template file path
  
  // Print Area Specifications
  printAreaX  Int         // X offset for design placement
  printAreaY  Int         // Y offset for design placement
  printWidth  Int         // Maximum print width in pixels
  printHeight Int         // Maximum print height in pixels
  
  // Output Specifications
  outputWidth  Int        // Final output width
  outputHeight Int        // Final output height
  outputDPI    Int        @default(300)
  
  isDefault   Boolean     @default(false)
  isActive    Boolean     @default(true)
  
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  @@map("templates")
}
```

---

## 3. Folder Structure & Architecture

### 3.1 Project Structure

```
ravprints/
├── prisma/
│   ├── schema.prisma
│   ├── migrations/
│   └── seed.ts
├── public/
│   ├── templates/           # Product template files
│   │   ├── tshirt/
│   │   ├── mug/
│   │   ├── mousepad/
│   │   └── tote-bag/
│   └── uploads/             # Temporary file storage
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── (auth)/
│   │   │   ├── login/
│   │   │   └── register/
│   │   ├── (dashboard)/
│   │   │   ├── orders/
│   │   │   ├── designs/
│   │   │   ├── templates/
│   │   │   └── settings/
│   │   ├── api/
│   │   │   ├── auth/
│   │   │   ├── orders/
│   │   │   ├── designs/
│   │   │   ├── templates/
│   │   │   └── export/
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── components/
│   │   ├── ui/              # shadcn/ui components
│   │   ├── forms/
│   │   ├── tables/
│   │   ├── modals/
│   │   ├── preview/
│   │   └── layout/
│   ├── lib/
│   │   ├── prisma.ts
│   │   ├── auth.ts
│   │   ├── utils.ts
│   │   ├── excel-parser.ts
│   │   ├── image-processor.ts
│   │   ├── design-matcher.ts
│   │   └── export-generator.ts
│   ├── stores/
│   │   ├── order-store.ts
│   │   ├── design-store.ts
│   │   └── ui-store.ts
│   ├── types/
│   │   ├── order.ts
│   │   ├── design.ts
│   │   └── template.ts
│   └── hooks/
│       ├── use-orders.ts
│       ├── use-designs.ts
│       └── use-templates.ts
├── storage/                 # Local file storage
│   ├── designs/
│   ├── exports/
│   └── temp/
├── docs/
│   ├── PLANS.md
│   ├── API.md
│   └── DEPLOYMENT.md
└── tests/
    ├── __mocks__/
    ├── components/
    ├── lib/
    └── api/
```

### 3.2 Component Hierarchy

```
App Layout
├── AuthProvider (NextAuth)
├── QueryProvider (React Query)
├── StoreProvider (Zustand)
└── Dashboard Layout
    ├── Navigation
    ├── Sidebar
    └── Main Content
        ├── Orders Module
        │   ├── OrderUpload
        │   ├── OrderTable
        │   ├── OrderPreview
        │   └── OrderExport
        ├── Designs Module
        │   ├── DesignLibrary
        │   ├── DesignUpload
        │   └── DesignPicker
        └── Templates Module
            ├── TemplateManager
            └── TemplateEditor
```

---

## 4. Data Flow & Processing Pipeline

### 4.1 Order Processing Workflow

```mermaid
graph TD
    A[Upload CSV/XLSX] --> B[Parse Order Data]
    B --> C[Store Orders in Database]
    C --> D[Auto-Match Designs]
    D --> E{Match Found?}
    E -->|Yes| F[Generate Preview]
    E -->|No| G[Flag for Manual Review]
    G --> H[Manual Design Selection]
    H --> F
    F --> I[User Approval]
    I --> J{Approved?}
    J -->|Yes| K[Generate Print-Ready File]
    J -->|No| L[Edit/Retry]
    L --> F
    K --> M[Store Export File]
    M --> N[Add to Export Queue]
    N --> O[Batch Export ZIP]
```

### 4.2 Design Matching Algorithm

**Matching Strategy (Priority Order):**
1. **Exact Code Match**: `designCode` === `design.code`
2. **Filename Pattern Match**: Extract codes from filenames using regex patterns
3. **Tag-based Match**: Search design tags for order keywords
4. **Fuzzy String Match**: Use Levenshtein distance for similar names
5. **Manual Fallback**: Flag for user intervention

**Matching Confidence Scores:**
- Exact Match: 100%
- Pattern Match: 80-95%
- Tag Match: 60-80%
- Fuzzy Match: 40-70%
- Below 40%: Requires manual review

### 4.3 Image Processing Pipeline

**Template Processing Steps:**
1. Load product template (SVG/PNG)
2. Load design file (PNG/JPG/SVG/PSD)
3. Calculate target dimensions based on template specifications
4. Resize design maintaining aspect ratio
5. Apply positioning (center, fit, fill, custom)
6. Composite design onto template
7. Apply print specifications (DPI, color profile)
8. Export as high-resolution PNG/PDF

**Quality Control:**
- Minimum 300 DPI for all exports
- Color space conversion (RGB to CMYK for print)
- Bleed area calculation for edge-to-edge prints
- Resolution warnings for low-quality source images

---

## 5. API Specifications

### 5.1 Core API Endpoints

| Endpoint | Method | Description | Request Body | Response |
|----------|--------|-------------|--------------|----------|
| `/api/orders/upload` | POST | Upload and parse order file | `multipart/form-data` | `{orders: Order[], errors: string[]}` |
| `/api/orders` | GET | List orders with filters | Query params | `{orders: Order[], total: number}` |
| `/api/orders/[id]` | GET | Get order details | - | `{order: Order}` |
| `/api/orders/[id]/match` | POST | Auto-match design to order | `{designId?: string}` | `{order: Order, confidence: number}` |
| `/api/orders/[id]/preview` | GET | Generate order preview | - | `{previewUrl: string}` |
| `/api/designs` | GET | List designs with search | Query params | `{designs: Design[], total: number}` |
| `/api/designs/upload` | POST | Upload design files | `multipart/form-data` | `{designs: Design[]}` |
| `/api/designs/[id]` | GET | Get design details | - | `{design: Design}` |
| `/api/templates` | GET | List product templates | - | `{templates: Template[]}` |
| `/api/templates/[id]` | GET | Get template specifications | - | `{template: Template}` |
| `/api/export/batch` | POST | Generate batch export | `{orderIds: string[]}` | `{exportUrl: string, jobId: string}` |
| `/api/export/status/[jobId]` | GET | Check export job status | - | `{status: string, progress: number}` |

### 5.2 Authentication & Authorization

**NextAuth.js Configuration:**
- Providers: Email/Password, Google OAuth (optional)
- Session strategy: JWT
- Role-based access control (STAFF, ADMIN)
- Protected API routes with middleware

**Authorization Levels:**
- **STAFF**: Can upload orders, match designs, generate exports
- **ADMIN**: All STAFF permissions + user management, template editing

---

## 6. Image Processing & Template System

### 6.1 Template Specifications

**T-Shirt Templates:**
- Front Print: 12" x 16" @ 300 DPI (3600 x 4800 px)
- Back Print: 12" x 16" @ 300 DPI (3600 x 4800 px)
- Pocket Print: 4" x 4" @ 300 DPI (1200 x 1200 px)
- Print Area Offset: Center alignment with 2" margins

**Mug Templates:**
- Wrap Print: 8.5" x 3.75" @ 300 DPI (2550 x 1125 px)
- Handle Area: Exclude 2" width for handle placement
- Bleed Area: 0.125" on all sides

**Mousepad Templates:**
- Standard: 9" x 7.75" @ 300 DPI (2700 x 2325 px)
- Large: 12" x 10" @ 300 DPI (3600 x 3000 px)
- Full bleed with rounded corners

**Tote Bag Templates:**
- Front/Back: 12" x 12" @ 300 DPI (3600 x 3600 px)
- Handle clearance: 3" from top edge

### 6.2 Image Processing Library (Sharp.js)

**Core Processing Functions:**
```javascript
// Resize design to fit template
const resizeDesign = async (inputPath, width, height, fit = 'inside') => {
  return sharp(inputPath)
    .resize(width, height, { fit, background: { r: 255, g: 255, b: 255, alpha: 0 } })
    .png({ quality: 100 })
    .toBuffer();
};

// Composite design onto template
const compositeLayout = async (templatePath, designBuffer, x, y) => {
  return sharp(templatePath)
    .composite([{ input: designBuffer, left: x, top: y }])
    .png({ quality: 100, compressionLevel: 0 })
    .toFile(outputPath);
};

// Generate print-ready export
const generatePrintFile = async (layoutPath, dpi = 300) => {
  return sharp(layoutPath)
    .png({ quality: 100, compressionLevel: 0 })
    .withMetadata({ density: dpi })
    .toFile(exportPath);
};
```

### 6.3 File Format Support

**Input Formats:**
- PNG, JPG, JPEG (raster images)
- SVG (vector graphics)
- PSD (Photoshop files via sharp-psd)
- WEBP (modern web format)

**Output Formats:**
- PNG (primary for print)
- PDF (alternative for vector-based prints)
- TIFF (high-quality archival format)

---

## 7. Package Dependencies & Installation

### 7.1 Core Dependencies

**Production Dependencies:**
```json
{
  "next": "^15.0.0",
  "react": "^18.0.0",
  "react-dom": "^18.0.0",
  "typescript": "^5.0.0",
  "tailwindcss": "^4.0.0",
  "@tailwindcss/postcss": "^4.0.0",
  "prisma": "^5.0.0",
  "@prisma/client": "^5.0.0",
  "next-auth": "^4.24.0",
  "zustand": "^4.4.0",
  "sharp": "^0.33.0",
  "xlsx": "^0.18.0",
  "archiver": "^6.0.0",
  "pdf-lib": "^1.17.0",
  "react-hook-form": "^7.47.0",
  "@hookform/resolvers": "^3.3.0",
  "zod": "^3.22.0",
  "lucide-react": "^0.294.0",
  "class-variance-authority": "^0.7.0",
  "clsx": "^2.0.0",
  "tailwind-merge": "^2.0.0"
}
```

**Development Dependencies:**
```json
{
  "@types/node": "^20.0.0",
  "@types/react": "^18.0.0",
  "@types/react-dom": "^18.0.0",
  "@types/archiver": "^6.0.0",
  "eslint": "^8.0.0",
  "eslint-config-next": "^15.0.0",
  "prettier": "^3.0.0",
  "jest": "^29.0.0",
  "@testing-library/react": "^13.0.0",
  "@testing-library/jest-dom": "^6.0.0",
  "msw": "^2.0.0"
}
```

### 7.2 shadcn/ui Components

**Required Components:**
- Button, Input, Label, Textarea
- Table, DataTable with sorting/filtering
- Dialog, Sheet, Popover
- Select, Combobox, Command
- Progress, Badge, Alert
- Form components with validation
- Toast notifications
- Tabs, Accordion, Collapsible

---

## 8. Development Roadmap & Sprint Planning

### 8.1 Phase 1: Foundation (Weeks 1-2)

**Sprint 1: Project Setup & Authentication (Week 1)**
- [x] Initialize Next.js 15 project with TypeScript
- [x] Configure Tailwind CSS v4 and shadcn/ui
- [ ] Set up Prisma with Supabase connection
- [ ] Implement NextAuth.js with email/password
- [ ] Create basic layout and navigation
- [ ] Set up development environment and tooling

**Sprint 2: Database & User Management (Week 2)**
- [ ] Implement Prisma schema and migrations
- [ ] Create user registration and login flows
- [ ] Set up role-based access control
- [ ] Build basic dashboard layout
- [ ] Implement user profile management
- [ ] Add error handling and validation

### 8.2 Phase 2: Core Features (Weeks 3-6)

**Sprint 3: Order Management (Week 3)**
- [ ] Build order upload component (CSV/XLSX)
- [ ] Implement Excel parsing with SheetJS
- [ ] Create order listing and filtering
- [ ] Add order status management
- [ ] Build order detail view
- [ ] Implement batch operations

**Sprint 4: Design Management (Week 4)**
- [ ] Create design library interface
- [ ] Implement design file upload
- [ ] Build design search and filtering
- [ ] Add design metadata management
- [ ] Create design picker component
- [ ] Implement file organization system

**Sprint 5: Auto-Matching System (Week 5)**
- [ ] Develop design matching algorithms
- [ ] Implement confidence scoring
- [ ] Build matching review interface
- [ ] Add manual override functionality
- [ ] Create matching statistics dashboard
- [ ] Optimize matching performance

**Sprint 6: Template System (Week 6)**
- [ ] Create template management interface
- [ ] Implement template specifications
- [ ] Build template preview system
- [ ] Add template validation
- [ ] Create default templates for each product
- [ ] Implement template versioning

### 8.3 Phase 3: Image Processing (Weeks 7-8)

**Sprint 7: Image Processing Engine (Week 7)**
- [ ] Integrate Sharp.js for image manipulation
- [ ] Implement design resizing algorithms
- [ ] Build template composition system
- [ ] Add DPI and quality controls
- [ ] Create image format conversion
- [ ] Implement batch processing

**Sprint 8: Preview & Export System (Week 8)**
- [ ] Build real-time preview component
- [ ] Implement zoom and pan functionality
- [ ] Create export queue system
- [ ] Add ZIP generation for batch exports
- [ ] Implement export job tracking
- [ ] Build download management

### 8.4 Phase 4: Polish & Testing (Weeks 9-10)

**Sprint 9: UI/UX Polish (Week 9)**
- [ ] Refine component styling and interactions
- [ ] Add loading states and animations
- [ ] Implement comprehensive error handling
- [ ] Add keyboard shortcuts and accessibility
- [ ] Create responsive design optimizations
- [ ] Build help documentation and tooltips

**Sprint 10: Testing & Deployment (Week 10)**
- [ ] Write comprehensive unit tests
- [ ] Implement integration tests
- [ ] Add end-to-end testing with Playwright
- [ ] Performance optimization and profiling
- [ ] Security audit and fixes
- [ ] Production deployment preparation

### 8.5 Success Metrics & KPIs

**Performance Targets:**
- Order processing time: < 2 minutes per order
- Batch processing: 50+ orders in < 10 minutes
- Design matching accuracy: > 85% auto-match rate
- Export generation: < 30 seconds per order
- System uptime: > 99% availability

**User Experience Metrics:**
- Time to first successful export: < 5 minutes
- User error rate: < 5% of operations
- Support ticket reduction: > 80%
- User satisfaction score: > 4.5/5

---

## 9. Implementation Examples

### 9.1 Zustand Store Examples

**Order Store:**
```typescript
// src/stores/order-store.ts
import { create } from 'zustand';
import { Order, OrderStatus } from '@/types/order';

interface OrderStore {
  orders: Order[];
  selectedOrders: string[];
  filters: OrderFilters;
  loading: boolean;

  // Actions
  setOrders: (orders: Order[]) => void;
  addOrders: (orders: Order[]) => void;
  updateOrder: (id: string, updates: Partial<Order>) => void;
  selectOrder: (id: string) => void;
  selectMultiple: (ids: string[]) => void;
  clearSelection: () => void;
  setFilters: (filters: Partial<OrderFilters>) => void;
  setLoading: (loading: boolean) => void;
}

export const useOrderStore = create<OrderStore>((set, get) => ({
  orders: [],
  selectedOrders: [],
  filters: { status: 'all', platform: 'all', productType: 'all' },
  loading: false,

  setOrders: (orders) => set({ orders }),

  addOrders: (newOrders) => set((state) => ({
    orders: [...state.orders, ...newOrders]
  })),

  updateOrder: (id, updates) => set((state) => ({
    orders: state.orders.map(order =>
      order.id === id ? { ...order, ...updates } : order
    )
  })),

  selectOrder: (id) => set((state) => ({
    selectedOrders: state.selectedOrders.includes(id)
      ? state.selectedOrders.filter(orderId => orderId !== id)
      : [...state.selectedOrders, id]
  })),

  selectMultiple: (ids) => set({ selectedOrders: ids }),
  clearSelection: () => set({ selectedOrders: [] }),
  setFilters: (filters) => set((state) => ({ filters: { ...state.filters, ...filters } })),
  setLoading: (loading) => set({ loading })
}));
```

**Design Store:**
```typescript
// src/stores/design-store.ts
import { create } from 'zustand';
import { Design } from '@/types/design';

interface DesignStore {
  designs: Design[];
  searchQuery: string;
  selectedDesign: Design | null;
  loading: boolean;

  // Actions
  setDesigns: (designs: Design[]) => void;
  addDesign: (design: Design) => void;
  updateDesign: (id: string, updates: Partial<Design>) => void;
  setSearchQuery: (query: string) => void;
  setSelectedDesign: (design: Design | null) => void;
  searchDesigns: (query: string) => Design[];
}

export const useDesignStore = create<DesignStore>((set, get) => ({
  designs: [],
  searchQuery: '',
  selectedDesign: null,
  loading: false,

  setDesigns: (designs) => set({ designs }),

  addDesign: (design) => set((state) => ({
    designs: [...state.designs, design]
  })),

  updateDesign: (id, updates) => set((state) => ({
    designs: state.designs.map(design =>
      design.id === id ? { ...design, ...updates } : design
    )
  })),

  setSearchQuery: (searchQuery) => set({ searchQuery }),
  setSelectedDesign: (selectedDesign) => set({ selectedDesign }),

  searchDesigns: (query) => {
    const { designs } = get();
    if (!query) return designs;

    return designs.filter(design =>
      design.code.toLowerCase().includes(query.toLowerCase()) ||
      design.filename.toLowerCase().includes(query.toLowerCase()) ||
      design.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
    );
  }
}));
```

### 9.2 API Route Examples

**Order Upload API:**
```typescript
// src/app/api/orders/upload/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { parseExcelFile } from '@/lib/excel-parser';
import { prisma } from '@/lib/prisma';
import { authOptions } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Parse Excel/CSV file
    const buffer = await file.arrayBuffer();
    const parsedOrders = await parseExcelFile(buffer, file.name);

    // Store orders in database
    const orders = await Promise.all(
      parsedOrders.map(async (orderData) => {
        return prisma.order.create({
          data: {
            ...orderData,
            userId: session.user.id,
            status: 'PENDING'
          }
        });
      })
    );

    return NextResponse.json({
      success: true,
      orders,
      count: orders.length
    });

  } catch (error) {
    console.error('Order upload error:', error);
    return NextResponse.json(
      { error: 'Failed to process order file' },
      { status: 500 }
    );
  }
}
```

**Design Matching API:**
```typescript
// src/app/api/orders/[id]/match/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { matchDesignToOrder } from '@/lib/design-matcher';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { designId } = await request.json();
    const orderId = params.id;

    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: { design: true }
    });

    if (!order) {
      return NextResponse.json({ error: 'Order not found' }, { status: 404 });
    }

    let matchResult;

    if (designId) {
      // Manual design selection
      const design = await prisma.design.findUnique({
        where: { id: designId }
      });

      if (!design) {
        return NextResponse.json({ error: 'Design not found' }, { status: 404 });
      }

      matchResult = { design, confidence: 100 };
    } else {
      // Auto-match design
      matchResult = await matchDesignToOrder(order);
    }

    // Update order with matched design
    const updatedOrder = await prisma.order.update({
      where: { id: orderId },
      data: {
        designId: matchResult.design.id,
        matched: true,
        status: 'MATCHED'
      },
      include: { design: true }
    });

    return NextResponse.json({
      order: updatedOrder,
      confidence: matchResult.confidence
    });

  } catch (error) {
    console.error('Design matching error:', error);
    return NextResponse.json(
      { error: 'Failed to match design' },
      { status: 500 }
    );
  }
}
```

### 9.3 Image Processing Examples

**Design Matcher Implementation:**
```typescript
// src/lib/design-matcher.ts
import { prisma } from './prisma';
import { Order, Design } from '@prisma/client';

interface MatchResult {
  design: Design;
  confidence: number;
}

export async function matchDesignToOrder(order: Order): Promise<MatchResult | null> {
  const designs = await prisma.design.findMany();

  if (!order.designCode) {
    return null;
  }

  // 1. Exact code match
  const exactMatch = designs.find(design =>
    design.code.toLowerCase() === order.designCode?.toLowerCase()
  );

  if (exactMatch) {
    return { design: exactMatch, confidence: 100 };
  }

  // 2. Filename pattern match
  const filenameMatch = designs.find(design =>
    design.filename.toLowerCase().includes(order.designCode?.toLowerCase() || '')
  );

  if (filenameMatch) {
    return { design: filenameMatch, confidence: 85 };
  }

  // 3. Tag-based match
  const tagMatch = designs.find(design =>
    design.tags.some(tag =>
      tag.toLowerCase().includes(order.designCode?.toLowerCase() || '')
    )
  );

  if (tagMatch) {
    return { design: tagMatch, confidence: 70 };
  }

  // 4. Fuzzy string match
  const fuzzyMatches = designs
    .map(design => ({
      design,
      confidence: calculateSimilarity(order.designCode || '', design.code)
    }))
    .filter(match => match.confidence > 40)
    .sort((a, b) => b.confidence - a.confidence);

  return fuzzyMatches.length > 0 ? fuzzyMatches[0] : null;
}

function calculateSimilarity(str1: string, str2: string): number {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;

  if (longer.length === 0) return 100;

  const distance = levenshteinDistance(longer, shorter);
  return Math.round(((longer.length - distance) / longer.length) * 100);
}

function levenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() =>
    Array(str1.length + 1).fill(null)
  );

  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,
        matrix[j - 1][i] + 1,
        matrix[j - 1][i - 1] + indicator
      );
    }
  }

  return matrix[str2.length][str1.length];
}
```

---

## 10. Deployment & Production Considerations

### 10.1 Environment Configuration

**Environment Variables:**
```bash
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/ravprints"
DIRECT_URL="postgresql://username:password@localhost:5432/ravprints"

# Authentication
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key"

# File Storage
UPLOAD_DIR="./storage/uploads"
DESIGN_DIR="./storage/designs"
EXPORT_DIR="./storage/exports"
TEMP_DIR="./storage/temp"

# Image Processing
MAX_FILE_SIZE="50MB"
SUPPORTED_FORMATS="png,jpg,jpeg,svg,psd,webp"
DEFAULT_DPI="300"

# Performance
MAX_CONCURRENT_EXPORTS="5"
EXPORT_TIMEOUT="300000"
```

### 10.2 Production Deployment

**Local Development:**
```bash
# Install dependencies
npm install

# Set up database
npx prisma generate
npx prisma db push
npx prisma db seed

# Start development server
npm run dev
```

**Production Build:**
```bash
# Build application
npm run build

# Start production server
npm start
```

### 10.3 Performance Optimization

**Image Processing Optimization:**
- Use Sharp.js worker threads for concurrent processing
- Implement image caching for frequently used designs
- Optimize template loading with lazy loading
- Use progressive image loading for previews

**Database Optimization:**
- Index frequently queried fields (designCode, orderId, status)
- Implement connection pooling
- Use database transactions for batch operations
- Regular database maintenance and cleanup

**File System Optimization:**
- Organize files in date-based directory structure
- Implement file cleanup for temporary files
- Use compression for archived exports
- Monitor disk space usage

---

## 11. Future Enhancements & Scaling

### 11.1 Phase 2 Features (Months 3-6)

**Advanced Features:**
- Cloud storage integration (AWS S3, Google Cloud)
- Real-time collaboration and notifications
- Advanced template editor with drag-drop
- Automated quality control and validation
- Integration with printing service APIs
- Mobile-responsive design and PWA support

**Analytics & Reporting:**
- Processing time analytics
- Design usage statistics
- Error tracking and monitoring
- Performance dashboards
- Customer order insights

### 11.2 Scaling Considerations

**Technical Scaling:**
- Microservices architecture for image processing
- Redis caching for session and data management
- CDN integration for static assets
- Load balancing for high availability
- Database sharding for large datasets

**Business Scaling:**
- Multi-tenant architecture for multiple businesses
- White-label solution for resellers
- API marketplace for third-party integrations
- Automated billing and subscription management
- Enterprise features and SLA support

---

## 12. Risk Assessment & Mitigation

### 12.1 Technical Risks

| Risk | Impact | Probability | Mitigation Strategy |
|------|--------|-------------|-------------------|
| Image processing performance bottlenecks | High | Medium | Implement worker queues, optimize Sharp.js usage |
| Large file handling issues | Medium | High | File size limits, streaming uploads, compression |
| Database performance with large datasets | High | Medium | Proper indexing, query optimization, pagination |
| Memory leaks in image processing | Medium | Medium | Proper resource cleanup, monitoring, limits |

### 12.2 Business Risks

| Risk | Impact | Probability | Mitigation Strategy |
|------|--------|-------------|-------------------|
| Design file organization complexity | Medium | High | Flexible tagging system, search functionality |
| User adoption resistance | High | Medium | Comprehensive training, gradual rollout |
| Print quality issues | High | Low | Extensive testing, quality controls, validation |
| Data loss or corruption | High | Low | Regular backups, version control, redundancy |

---

## Conclusion

This comprehensive plan provides a roadmap for building a robust, scalable print automation MVP that will significantly reduce manual processing time while maintaining high quality standards. The phased approach allows for iterative development and early user feedback, ensuring the final product meets business requirements and user expectations.

**Next Steps:**
1. Review and approve the technical architecture
2. Set up development environment and initial project structure
3. Begin Phase 1 implementation with Sprint 1 tasks
4. Establish regular review cycles and feedback loops
5. Plan user testing and training sessions

The success of this project will be measured by the reduction in processing time, improvement in accuracy, and overall user satisfaction with the automated workflow.
