'use client'

import { useSearchParams } from 'next/navigation'
import { useEffect } from 'react'

interface SearchParamsHandlerProps {
  onMessage?: (message: string) => void
  onCallbackUrl?: (url: string) => void
}

export function SearchParamsHandler({ onMessage, onCallbackUrl }: SearchParamsHandlerProps) {
  const searchParams = useSearchParams()

  useEffect(() => {
    const message = searchParams.get('message')
    const callbackUrl = searchParams.get('callbackUrl')
    
    if (message && onMessage) {
      onMessage(message)
    }
    
    if (callbackUrl && onCallbackUrl) {
      onCallbackUrl(callbackUrl)
    }
  }, [searchParams, onMessage, onCallbackUrl])

  return null
}
