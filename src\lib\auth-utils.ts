import { getServerSession } from 'next-auth'
import { redirect } from 'next/navigation'
import { authOptions } from './auth'

/**
 * Server-side utility to get the current session
 * Throws an error if no session is found
 */
export async function getRequiredSession() {
  const session = await getServerSession(authOptions)

  if (!session) {
    throw new Error('No session found')
  }

  return session
}

/**
 * Server-side utility to protect pages that require authentication
 * Redirects to signin if no session is found
 */
export async function requireAuth(callbackUrl?: string) {
  const session = await getServerSession(authOptions)

  if (!session) {
    const signInUrl = callbackUrl
      ? `/signin?callbackUrl=${encodeURIComponent(callbackUrl)}`
      : '/signin'
    redirect(signInUrl)
  }

  return session
}

/**
 * Server-side utility to protect auth pages from authenticated users
 * Redirects to dashboard if session is found
 */
export async function requireGuest(redirectTo: string = '/dashboard') {
  const session = await getServerSession(authOptions)

  if (session) {
    redirect(redirectTo)
  }

  return null
}

/**
 * Server-side utility to check if user has required role
 */
export async function requireRole(
  requiredRole: 'STAFF' | 'ADMIN',
  callbackUrl?: string
) {
  const session = await requireAuth(callbackUrl)

  if (session.user.role !== requiredRole && session.user.role !== 'ADMIN') {
    redirect('/dashboard?error=insufficient_permissions')
  }

  return session
}

/**
 * Server-side utility to get session without throwing errors
 * Returns null if no session is found
 */
export async function getOptionalSession() {
  return await getServerSession(authOptions)
}
