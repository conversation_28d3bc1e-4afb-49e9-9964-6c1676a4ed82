'use client'

import { useSession } from 'next-auth/react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useEffect, ReactNode } from 'react'

interface AuthGuardProps {
  children: ReactNode
  requireAuth?: boolean
  redirectTo?: string
  fallback?: ReactNode
}

/**
 * AuthGuard component that handles authentication-based routing
 *
 * @param children - The content to render when auth conditions are met
 * @param requireAuth - Whether authentication is required (default: false)
 * @param redirectTo - Where to redirect if auth condition is not met
 * @param fallback - What to show while checking authentication status
 */
export function AuthGuard({
  children,
  requireAuth = false,
  redirectTo,
  fallback,
}: AuthGuardProps) {
  const { status } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    if (status === 'loading') return // Still loading

    const callbackUrl = searchParams.get('callbackUrl')

    if (requireAuth) {
      // Protected route - redirect unauthenticated users
      if (status === 'unauthenticated') {
        const signInUrl = redirectTo || '/signin'
        const url = new URL(signInUrl, window.location.origin)
        if (callbackUrl) {
          url.searchParams.set('callbackUrl', callbackUrl)
        }
        router.replace(url.toString())
      }
    } else {
      // Auth route - redirect authenticated users
      if (status === 'authenticated') {
        const destination = callbackUrl || redirectTo || '/dashboard'
        router.replace(destination)
      }
    }
  }, [status, router, requireAuth, redirectTo, searchParams])

  // Show loading state
  if (status === 'loading') {
    return (
      fallback || (
        <div className="flex h-screen items-center justify-center">
          <div className="flex flex-col items-center space-y-4">
            <div className="h-8 w-8 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"></div>
            <p className="text-sm text-gray-600">Loading...</p>
          </div>
        </div>
      )
    )
  }

  // For protected routes, only render if authenticated
  if (requireAuth && status !== 'authenticated') {
    return null
  }

  // For auth routes, only render if not authenticated
  if (!requireAuth && status === 'authenticated') {
    return null
  }

  return <>{children}</>
}

/**
 * Higher-order component for protecting routes that require authentication
 */
export function withAuthGuard<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    redirectTo?: string
    fallback?: ReactNode
  }
) {
  return function AuthGuardedComponent(props: P) {
    return (
      <AuthGuard
        requireAuth={true}
        redirectTo={options?.redirectTo}
        fallback={options?.fallback}
      >
        <Component {...props} />
      </AuthGuard>
    )
  }
}

/**
 * Higher-order component for auth pages that should redirect authenticated users
 */
export function withGuestGuard<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    redirectTo?: string
    fallback?: ReactNode
  }
) {
  return function GuestGuardedComponent(props: P) {
    return (
      <AuthGuard
        requireAuth={false}
        redirectTo={options?.redirectTo}
        fallback={options?.fallback}
      >
        <Component {...props} />
      </AuthGuard>
    )
  }
}
