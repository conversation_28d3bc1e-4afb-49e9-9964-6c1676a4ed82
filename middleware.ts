import { withA<PERSON> } from 'next-auth/middleware'
import { NextResponse } from 'next/server'
import type { NextRequestWithAuth } from 'next-auth/middleware'

export default withAuth(
  function middleware(req: NextRequestWithAuth) {
    const token = req.nextauth.token
    const { pathname } = req.nextUrl

    // Define auth routes that should redirect authenticated users
    const authRoutes = ['/signin', '/signup']

    // Define protected routes that require authentication
    const protectedRoutes = ['/dashboard']

    // If user is authenticated and trying to access auth pages
    if (token && authRoutes.includes(pathname)) {
      // Redirect to dashboard or intended destination
      const callbackUrl = req.nextUrl.searchParams.get('callbackUrl')
      const redirectUrl = callbackUrl || '/dashboard'

      return NextResponse.redirect(new URL(redirectUrl, req.url))
    }

    // If user is not authenticated and trying to access protected routes
    if (!token && protectedRoutes.some(route => pathname.startsWith(route))) {
      // Redirect to signin with callback URL
      const signInUrl = new URL('/signin', req.url)
      signInUrl.searchParams.set('callbackUrl', pathname)

      return NextResponse.redirect(signInUrl)
    }

    // Allow the request to continue
    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: () => {
        // Always return true to let the middleware function handle the logic
        return true
      },
    },
  }
)

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth.js API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
