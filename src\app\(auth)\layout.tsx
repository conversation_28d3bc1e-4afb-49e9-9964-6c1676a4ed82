import { ReactNode } from 'react'

interface AuthLayoutProps {
  children: ReactNode
}

export default function AuthLayout({ children }: AuthLayoutProps) {
  return (
    <div className="flex min-h-screen">
      {/* Left Side - Branding */}
      <div className="relative hidden overflow-hidden bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 lg:flex lg:w-1/2">
        <div className="absolute inset-0 bg-black/20" />
        <div className="relative z-10 flex flex-col justify-center px-12 text-white">
          <div className="mb-8">
            <div className="mb-6 flex items-center space-x-3">
              <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-white/20 backdrop-blur-sm">
                <span className="text-xl font-bold text-white">RP</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold">RavPrints</h1>
                <p className="text-sm text-blue-100">Print Automation System</p>
              </div>
            </div>
            <h2 className="mb-4 text-4xl leading-tight font-bold">
              Streamline Your Print Business
            </h2>
            <p className="mb-8 text-xl leading-relaxed text-blue-100">
              Automate order processing, manage designs, and generate
              print-ready files with our comprehensive platform.
            </p>
          </div>

          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="h-2 w-2 rounded-full bg-white" />
              <span className="text-blue-100">Automated order matching</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="h-2 w-2 rounded-full bg-white" />
              <span className="text-blue-100">Design library management</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="h-2 w-2 rounded-full bg-white" />
              <span className="text-blue-100">Batch export generation</span>
            </div>
          </div>
        </div>

        {/* Decorative elements */}
        <div className="absolute top-0 right-0 h-64 w-64 translate-x-32 -translate-y-32 rounded-full bg-white/5" />
        <div className="absolute bottom-0 left-0 h-48 w-48 -translate-x-24 translate-y-24 rounded-full bg-white/5" />
      </div>

      {/* Right Side - Auth Form */}
      <div className="flex flex-1 items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
        <div className="w-full max-w-md space-y-8">
          {/* Mobile Logo */}
          <div className="text-center lg:hidden">
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-blue-600 to-blue-700 shadow-lg">
              <span className="text-lg font-bold text-white">RP</span>
            </div>
            <h2 className="mt-4 text-2xl font-bold text-gray-900">RavPrints</h2>
            <p className="mt-1 text-sm text-gray-600">
              Print Automation System
            </p>
          </div>
          {children}
        </div>
      </div>
    </div>
  )
}
