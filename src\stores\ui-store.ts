import { create } from 'zustand'

interface UIStore {
  // Sidebar
  sidebarOpen: boolean
  sidebarCollapsed: boolean

  // Modals
  modals: {
    orderUpload: boolean
    designPicker: boolean
    orderPreview: boolean
    exportProgress: boolean
  }

  // Loading states
  loadingStates: {
    orders: boolean
    designs: boolean
    export: boolean
    matching: boolean
  }

  // Notifications
  notifications: Array<{
    id: string
    type: 'success' | 'error' | 'warning' | 'info'
    title: string
    message?: string
    duration?: number
  }>

  // Actions
  setSidebarOpen: (open: boolean) => void
  setSidebarCollapsed: (collapsed: boolean) => void
  toggleSidebar: () => void

  openModal: (modal: keyof UIStore['modals']) => void
  closeModal: (modal: keyof UIStore['modals']) => void
  closeAllModals: () => void

  setLoading: (key: keyof UIStore['loadingStates'], loading: boolean) => void

  addNotification: (
    notification: Omit<UIStore['notifications'][0], 'id'>
  ) => void
  removeNotification: (id: string) => void
  clearNotifications: () => void
}

export const useUIStore = create<UIStore>((set, _get) => ({
  sidebarOpen: true,
  sidebarCollapsed: false,

  modals: {
    orderUpload: false,
    designPicker: false,
    orderPreview: false,
    exportProgress: false,
  },

  loadingStates: {
    orders: false,
    designs: false,
    export: false,
    matching: false,
  },

  notifications: [],

  setSidebarOpen: sidebarOpen => set({ sidebarOpen }),
  setSidebarCollapsed: sidebarCollapsed => set({ sidebarCollapsed }),
  toggleSidebar: () => set(state => ({ sidebarOpen: !state.sidebarOpen })),

  openModal: modal =>
    set(state => ({
      modals: { ...state.modals, [modal]: true },
    })),

  closeModal: modal =>
    set(state => ({
      modals: { ...state.modals, [modal]: false },
    })),

  closeAllModals: () =>
    set(state => ({
      modals: Object.keys(state.modals).reduce(
        (acc, key) => ({
          ...acc,
          [key]: false,
        }),
        {} as UIStore['modals']
      ),
    })),

  setLoading: (key, loading) =>
    set(state => ({
      loadingStates: { ...state.loadingStates, [key]: loading },
    })),

  addNotification: notification =>
    set(state => ({
      notifications: [
        ...state.notifications,
        {
          ...notification,
          id: Math.random().toString(36).substr(2, 9),
        },
      ],
    })),

  removeNotification: id =>
    set(state => ({
      notifications: state.notifications.filter(n => n.id !== id),
    })),

  clearNotifications: () => set({ notifications: [] }),
}))
