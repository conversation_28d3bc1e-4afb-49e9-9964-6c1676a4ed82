// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// User Management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String?
  role      UserRole @default(STAFF)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  orders    Order[]

  @@map("users")
}

enum UserRole {
  STAFF
  ADMIN
}

// Order Management
model Order {
  id           String      @id @default(cuid())
  orderId      String      @unique // Platform order ID
  platform     Platform    // Source platform
  productType  ProductType
  size         String?
  designCode   String?
  quantity     Int         @default(1)
  customerName String?
  customerInfo Json?       // Flexible customer data

  // Processing Status
  status       OrderStatus @default(PENDING)
  matched      Boolean     @default(false)
  processed    Boolean     @default(false)

  // File References
  designId     String?
  design       Design?     @relation(fields: [designId], references: [id])
  layoutFileUrl String?    // Generated layout file path

  // Metadata
  userId       String
  user         User        @relation(fields: [userId], references: [id])
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  @@map("orders")
}

enum Platform {
  LAZADA
  SHOPEE
  TIKTOK
  MANUAL
}

enum ProductType {
  TSHIRT
  MUG
  MOUSEPAD
  TOTE_BAG
  CUSTOM
}

enum OrderStatus {
  PENDING
  MATCHED
  PROCESSING
  READY
  EXPORTED
  ERROR
}

// Design Management
model Design {
  id          String   @id @default(cuid())
  code        String   @unique // Design identifier
  filename    String
  originalName String?
  filePath    String   // Local file system path
  fileSize    Int?
  mimeType    String?

  // Metadata
  tags        String[] // Searchable tags
  description String?
  category    String?

  // Dimensions
  width       Int?
  height      Int?
  dpi         Int?

  // Relationships
  orders      Order[]

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("designs")
}

// Product Templates
model Template {
  id          String      @id @default(cuid())
  productType ProductType
  name        String
  filePath    String      // Template file path

  // Print Area Specifications
  printAreaX  Int         // X offset for design placement
  printAreaY  Int         // Y offset for design placement
  printWidth  Int         // Maximum print width in pixels
  printHeight Int         // Maximum print height in pixels

  // Output Specifications
  outputWidth  Int        // Final output width
  outputHeight Int        // Final output height
  outputDPI    Int        @default(300)

  isDefault   Boolean     @default(false)
  isActive    Boolean     @default(true)

  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  @@map("templates")
}
